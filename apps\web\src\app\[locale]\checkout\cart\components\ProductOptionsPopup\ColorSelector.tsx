'use client'
import Image from 'next/image'
import clsx from 'clsx'

import { SoldOutTag } from '@/components/icons'
import type { ProductOptionItem, ProductStatus, Variant } from '@/types/product'

interface ImageCompProps {
  id: string
  variants: Variant[]
}

interface ColorSelectorProps {
  id: string
  optionItems: ProductOptionItem[]
  variants: Variant[]
  productStatus: ProductStatus
  onSelectionChange: (item: ProductOptionItem, id: string) => void
}

const ImageComp: React.FC<ImageCompProps> = ({ id, variants }) => {
  const tempConfigUid: string[] = []
  let url = ''
  let label = ''

  variants.forEach((variant) => {
    if (
      variant.attributes &&
      variant.attributes.find((attr) => attr.code.includes('color'))?.uid === id &&
      !tempConfigUid.includes(id)
    ) {
      tempConfigUid.push(id)
      url = variant.product.image.url
      label = variant.product.name
    }
  })

  return url ? (
    <Image
      src={url}
      alt={label || 'product image'}
      className="object-contain"
      fill
      sizes="24px"
      priority
    />
  ) : null
}

const ColorSelector: React.FC<ColorSelectorProps> = ({
  id,
  optionItems,
  variants,
  productStatus,
  onSelectionChange,
}) => {
  const { optionSelections } = productStatus
  const optionId = optionSelections?.get(id)
  const { outOfStockVariants, isEverythingOutOfStock } = productStatus

  // 对选项进行排序，将售罄的选项移动到末尾
  const sortedOptionItems = [...optionItems].sort((a, b) => {
    let isAOutOfStock = false
    let isBOutOfStock = false

    if (outOfStockVariants && outOfStockVariants.length > 0) {
      const flatOutOfStockArray = outOfStockVariants.flat()
      isAOutOfStock = flatOutOfStockArray.includes(a.value_index)
      isBOutOfStock = flatOutOfStockArray.includes(b.value_index)
    }

    // 如果A售罄但B没有售罄，A排在后面
    if (isAOutOfStock && !isBOutOfStock) return 1
    // 如果B售罄但A没有售罄，B排在后面
    if (!isAOutOfStock && isBOutOfStock) return -1
    // 其他情况保持原有顺序
    return 0
  })

  return (
    <div className="flex flex-wrap gap-base-12">
      {sortedOptionItems.map((item) => {
        let isOptionOutOfStock = false
        if (outOfStockVariants && outOfStockVariants.length > 0) {
          const flatOutOfStockArray = outOfStockVariants.flat()
          isOptionOutOfStock = flatOutOfStockArray.includes(item.value_index)
        }
        return (
          <div key={item.uid} className="relative">
            <button
              className={clsx(
                'flex items-center gap-base rounded-full border px-base-16 py-base text-base',
                optionId === item.value_index
                  ? 'border-primary bg-[#EC1D260D] text-primary'
                  : 'bg-gray-base',
                isOptionOutOfStock && ['cursor-not-allowed'],
              )}
              onClick={() => {
                if (
                  !(isEverythingOutOfStock || isOptionOutOfStock) &&
                  optionId !== item.value_index
                ) {
                  onSelectionChange(item, id)
                }
              }}>
              <div className="relative h-[24px] w-[24px] rounded-base">
                <ImageComp id={item.uid || ''} variants={variants} />
              </div>
              <div className="text-lg">{item.label}</div>
            </button>
            {isOptionOutOfStock && (
              <div className="absolute -top-base right-0">
                <SoldOutTag />
              </div>
            )}
          </div>
        )
      })}
    </div>
  )
}

export default ColorSelector
