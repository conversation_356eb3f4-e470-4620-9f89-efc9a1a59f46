'use client'

import React, { useCallback } from 'react'
import { useTranslations } from 'next-intl'
import { mergeStyles, OrderPickupStore } from '@ninebot/core'

import { ActionSheet } from '@/businessComponents'
import { commonStyles } from '@/constants'
import { useLinking, useMapAction } from '@/hooks'

import { CustomButton, IconNavigation, IconPhone } from '..'

const styles = {
  ...commonStyles,
  headerContent: 'flex flex-row items-center',
  storeName:
    'text-[#000000] text-[16px] leading-none ml-[4px] font-miSansDemiBold450 leading-[22px]',
  content: 'px-[12px] py-[16px] bg-[#F8F8F9] rounded-[8px]',
  contentLeft: 'flex-1 mr-[16px]',
  status: 'text-[#444446] text-[14px] leading-8 font-miSansRegular330',
  contentRight: 'flex flex-row items-center gap-[16px]',
}

type PickupStoreCardProps = {
  storeInfo: OrderPickupStore
  showStoreName?: boolean
  onNavPress?: () => void
  rootStyle?: string
  contentStyle?: string
  storeNameStyle?: string
  addressStyle?: string
}

/**
 * 门店信息 Card
 */
const PickupStoreCard = (props: PickupStoreCardProps) => {
  const {
    storeInfo: {
      store_name,
      store_address,
      latitude,
      longitude,
      telephone,
      status,
      business_hours,
      closing_hours,
    },
    showStoreName = true,
    onNavPress,
    rootStyle = '',
    contentStyle = '',
    storeNameStyle = '',
    addressStyle = '',
  } = props
  const {
    isShowMap,
    mapActionSheetVisible,
    handleMapActionSheetClose,
    mapLinkActions,
    handleMapItemOpen,
    handleMapIconPress,
  } = useMapAction({
    address: store_address,
    latitude: latitude,
    longitude: longitude,
  })

  const { openLink } = useLinking()
  const getI18nString = useTranslations('Common')

  /**
   * 点击拨号事件
   */
  const handlePhonePress = useCallback(() => {
    if (telephone) {
      const url = `tel:${telephone}`
      openLink(url)
    }
  }, [telephone, openLink])

  /**
   * 点击打开手机地图事件
   */
  const handleNavPress = useCallback(async () => {
    if (onNavPress) {
      onNavPress()
      return
    }

    handleMapIconPress()
  }, [onNavPress, handleMapIconPress])

  return (
    <>
      <div className={rootStyle}>
        {showStoreName ? (
          <div className={mergeStyles([styles.flex_row, 'mb-[24px]'])}>
            <div className={styles.headerContent}>
              <div className={mergeStyles([styles.storeName, storeNameStyle])}>{store_name}</div>
            </div>
          </div>
        ) : null}
        <div className={mergeStyles([styles.flex_row, styles.content, contentStyle])}>
          <div className={styles.contentLeft}>
            <div className={styles.status}>
              <span className="mr-[4px]">
                {status ? getI18nString('product_store_business') : getI18nString('closed')}
              </span>
              {business_hours}-{closing_hours}
            </div>
            <div className={mergeStyles([styles.status, 'mt-[4px]', addressStyle])}>
              {store_address}
            </div>
          </div>
          <div className={styles.contentRight}>
            {telephone ? (
              <CustomButton
                customStyle={{
                  width: 24,
                  height: 24,
                  borderRadius: 4,
                  backgroundColor: '#E1E1E4',
                }}
                fill="none"
                onClick={handlePhonePress}>
                <IconPhone color="#E1E1E4" />
              </CustomButton>
            ) : null}
            {isShowMap ? (
              <CustomButton
                customStyle={{
                  width: 24,
                  height: 24,
                  borderRadius: 4,
                  backgroundColor: '#E1E1E4',
                }}
                fill="none"
                onClick={handleNavPress}>
                <IconNavigation color="#E1E1E4" />
              </CustomButton>
            ) : null}
          </div>
        </div>
      </div>

      {/* 展示选择地图 */}
      <ActionSheet
        visible={mapActionSheetVisible}
        onClose={handleMapActionSheetClose}
        actions={mapLinkActions}
        onAction={handleMapItemOpen}
      />
    </>
  )
}

export default PickupStoreCard
