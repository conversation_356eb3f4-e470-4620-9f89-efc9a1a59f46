'use client'
import React from 'react'
import { cn } from '@ninebot/core'

import { SoldOutTag } from '@/components'
import type { ProductStatus } from '@/types/product'
interface OptionItem {
  value_index: number
  label: string
}

interface SizeSelectorProps {
  optionItems: OptionItem[]
  id: string
  productStatus: ProductStatus
  onSelectionChange: (item: OptionItem, id: string) => void
}

const SizeSelector: React.FC<SizeSelectorProps> = ({
  optionItems,
  id,
  productStatus,
  onSelectionChange,
}) => {
  if (!productStatus) return null
  const { optionSelections, outOfStockVariants = [], isEverythingOutOfStock } = productStatus
  const optionId = optionSelections?.get(id)
  // 提前计算缺货数组的 Set
  const outOfStockSet = new Set(outOfStockVariants.flat())

  // 对选项进行排序，将售罄的选项移动到末尾
  const sortedOptionItems = [...optionItems].sort((a, b) => {
    const isAOutOfStock = outOfStockSet.has(a.value_index)
    const isBOutOfStock = outOfStockSet.has(b.value_index)

    // 如果A售罄但B没有售罄，A排在后面
    if (isAOutOfStock && !isBOutOfStock) return 1
    // 如果B售罄但A没有售罄，B排在后面
    if (!isAOutOfStock && isBOutOfStock) return -1
    // 其他情况保持原有顺序
    return 0
  })

  return (
    <div
      className={cn('grid grid-cols-3 gap-base-16', {
        'grid-cols-2': sortedOptionItems.length < 2,
      })}>
      {sortedOptionItems.map((item) => {
        const isOptionOutOfStock = outOfStockSet.has(item.value_index)
        return (
          <button
            key={item.value_index}
            disabled={isOptionOutOfStock}
            className={cn(
              'font-miSansRegular380 relative flex h-[48px] max-w-[156px] items-center justify-center rounded-base border p-base text-[16px] leading-[140%] transition-colors',
              optionId === item.value_index
                ? 'border-primary bg-[#DA291C0D]'
                : 'border-[#F8F8F9] bg-[#F8F8F9]',
              isOptionOutOfStock && 'cursor-not-allowed',
            )}
            onClick={() => {
              if (
                !(isEverythingOutOfStock || isOptionOutOfStock) &&
                optionId !== item.value_index
              ) {
                onSelectionChange(item, id)
              }
            }}>
            <span className={cn('text-center', isOptionOutOfStock && 'text-[#86868B]')}>
              {item.label}
            </span>
            {isOptionOutOfStock && (
              <div className="absolute -top-base right-0">
                <SoldOutTag />
              </div>
            )}
          </button>
        )
      })}
    </div>
  )
}

export default SizeSelector
