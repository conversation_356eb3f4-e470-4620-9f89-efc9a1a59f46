'use client'

import Image from 'next/image'
import { cn } from '@ninebot/core'

import { SoldOutTag } from '@/components'
import type { ProductOptionItem, ProductStatus, Variant } from '@/types/product'

interface ImageCompProps {
  item: ProductOptionItem
  variants: Variant[]
}

interface ColorSelectorProps {
  id: string
  optionItems: ProductOptionItem[]
  variants: Variant[]
  productStatus: ProductStatus
  onSelectionChange: (item: ProductOptionItem, id: string) => void
}

const ImageComp = ({ item, variants }: ImageCompProps) => {
  const tempConfigUid: string[] = []
  let url = ''
  let label = ''

  variants.forEach((i) => {
    if (
      i.attributes &&
      i.attributes.find((attr) => attr.code.includes('color'))?.uid === item.uid &&
      !tempConfigUid.includes(item.uid)
    ) {
      tempConfigUid.push(item.uid)
      url = i.product.image.url
      label = i.product.name
    }
  })
  return url ? (
    <Image src={url} alt={label} className="object-cover" width={32} height={32} priority />
  ) : null
}

const ColorSelector = ({
  id,
  optionItems,
  variants,
  productStatus,
  onSelectionChange,
}: ColorSelectorProps) => {
  const { optionSelections } = productStatus

  if (!optionSelections) return null
  const optionId = optionSelections.get(id)
  const { outOfStockVariants, isEverythingOutOfStock } = productStatus

  // 对选项进行排序，将售罄的选项移动到末尾
  const sortedOptionItems = [...optionItems].sort((a, b) => {
    let isAOutOfStock = false
    let isBOutOfStock = false

    if (outOfStockVariants && outOfStockVariants.length > 0) {
      const flatOutOfStockArray = outOfStockVariants.flat()
      isAOutOfStock = flatOutOfStockArray.includes(a.value_index)
      isBOutOfStock = flatOutOfStockArray.includes(b.value_index)
    }

    // 如果A售罄但B没有售罄，A排在后面
    if (isAOutOfStock && !isBOutOfStock) return 1
    // 如果B售罄但A没有售罄，B排在后面
    if (!isAOutOfStock && isBOutOfStock) return -1
    // 其他情况保持原有顺序
    return 0
  })

  return (
    <div
      className={cn('grid grid-cols-3 gap-base-16', {
        'grid-cols-2': sortedOptionItems.length < 2,
      })}>
      {sortedOptionItems.map((item) => {
        let isOptionOutOfStock = false
        if (outOfStockVariants && outOfStockVariants.length > 0) {
          const flatOutOfStockArray = outOfStockVariants.flat()
          isOptionOutOfStock = flatOutOfStockArray.includes(item.value_index)
        }
        return (
          <button
            key={item.uid}
            disabled={isOptionOutOfStock}
            className={cn(
              'gap-base-8 relative flex h-[48px] max-w-[156px] items-center justify-center gap-base rounded-base border p-base text-[16px] leading-[140%] transition-colors',
              optionId === item.value_index
                ? 'border-primary bg-[#DA291C0D]'
                : 'border-[#F8F8F9] bg-[#F8F8F9]',
              isOptionOutOfStock && 'cursor-not-allowed',
            )}
            onClick={() => {
              if (
                !(isEverythingOutOfStock || isOptionOutOfStock) &&
                optionId !== item.value_index
              ) {
                onSelectionChange(item, id)
              }
            }}>
            <div className={cn('flex-shrink-0', isOptionOutOfStock && 'opacity-50')}>
              <ImageComp item={item} variants={variants} />
            </div>
            <span
              className={cn(
                'font-miSansRegular380 line-clamp-1',
                isOptionOutOfStock && 'text-[#86868B]',
              )}>
              {item.label}
            </span>
            {isOptionOutOfStock && (
              <div className="absolute -top-base right-0">
                <SoldOutTag />
              </div>
            )}
          </button>
        )
      })}
    </div>
  )
}

export default ColorSelector
