{"name": "@ninebot", "private": true, "author": "Peter.xu <<EMAIL>>", "scripts": {"build": "turbo build", "dev": "turbo dev", "lint": "turbo lint", "clean": "turbo clean", "type-check": "turbo type-check", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "prepare": "husky", "clean:cache": "sh clean-cache.sh"}, "devDependencies": {"@commitlint/cli": "^19.7.1", "@commitlint/config-conventional": "^19.7.1", "@graphql-codegen/cli": "^5.0.5", "@graphql-codegen/client-preset": "^4.6.4", "@types/big.js": "^6.2.2", "@types/lodash-es": "^4.17.12", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/rtl-detect": "^1.0.3", "husky": "^9.1.6", "lint-staged": "^15.4.3", "prettier": "^3.5.2", "prettier-plugin-tailwindcss": "^0.6.11", "stylelint": "16.10.0", "stylelint-config-standard": "36.0.1", "stylelint-order": "6.0.4", "tailwindcss": "3.4.1", "turbo": "^2.4.4", "typescript": "5.5.4"}, "packageManager": "pnpm@9.7.1", "engines": {"node": ">=20.16.0", "pnpm": ">=9.7.1"}, "dependencies": {"@clerk/nextjs": "^6.12.1", "@hookform/resolvers": "^4.1.3", "@neshca/cache-handler": "^1.9.0", "ahooks": "^3.8.4", "antd": "^5.22.1", "big.js": "^6.2.2", "clsx": "^2.1.1", "lodash-es": "4.17.21", "next": "14.2.24", "next-intl": "^3.26.5", "pm2": "^5.4.2", "postcss": "^8.5.3", "react": "18.3.1", "react-dom": "18.3.1", "react-hook-form": "^7.54.2", "react-player": "^3.3.1", "redis": "4.7.0", "rtl-detect": "1.1.2", "tailwind-merge": "^3.0.2", "yup": "^1.6.1"}}