/* stylelint-disable at-rule-no-unknown */
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :where(.css-dev-only-do-not-override-*) {
    @apply all:m-0 all:p-0 all:box-border;
  }
}

:root {
  /* 默认值 */
  --nb-font-size: 10px;

  /* 视口高度相关变量 - 解决移动端兼容性问题 */
  --vh: 1vh; /* 默认值，会被 JavaScript 动态更新 */
}

html {
  font-size: var(--nb-font-size, 10px);
  transition: font-size 0.2s ease-in-out;
  text-size-adjust: 100%;
  /* 防止弹框出现时页面抖动 - 使用 scrollbar-gutter 预留滚动条空间 */
  scrollbar-gutter: stable;
}

html body {
  min-width: 1024px;
  width: 100% !important;
}

svg {
  backface-visibility: hidden;
  transform: translateZ(0); /* 或者 scale(1) 强制合成图层 */
}

/* 取消所有聚焦时的轮廓 */
:focus,
:focus-visible {
  outline: none !important;
}

/* #app必须保留 */
#app {
  height: 100%;
}

@import url('./components/pagination.css');
@import url('./components/scrollbar.css');
@import url('./components/input.css');
@import url('./components/drawer.css');
@import url('./components/dropdown.css');

/* ============ 移动端视口高度优化 ============ */

/* 使用真实视口高度的工具类 */
.h-screen-real {
  height: calc(var(--vh, 1vh) * 100);
}

.min-h-screen-real {
  min-height: calc(var(--vh, 1vh) * 100);
}

.max-h-screen-real {
  max-height: calc(var(--vh, 1vh) * 100);
}

/* 移动端安全区域适配 */
@supports (padding: max(0px)) {
  .pb-safe {
    padding-bottom: max(1rem, env(safe-area-inset-bottom));
  }

  .pt-safe {
    padding-top: max(1rem, env(safe-area-inset-top));
  }
}

/* 移动端滚动优化 */
@media (hover: none) and (pointer: coarse) {
  /* 防止整个页面的滚动穿透 */
  body {
    overscroll-behavior: contain;
  }

  /* 优化移动端触摸滚动 */
  .mobile-scroll-optimized {
    -webkit-overflow-scrolling: touch;
    overscroll-behavior: contain;
    touch-action: pan-y;
  }

  /* PAD 设备特殊优化 */
  @media (min-width: 768px) and (max-width: 1024px) {
    .scroll-container {
      /* PAD 设备上更严格的滚动控制 */
      overscroll-behavior: none;
      touch-action: pan-y pinch-zoom;
    }
  }
}

/* ============ 自定义样式覆盖 ============ */

/* Ant Design Rate */
.ant-rate-star-full,
.ant-rate-star-half {
  color: #da291c !important;
}

/* Antd Button Disabled style */
:where(.ant-btn).ant-btn-primary[disabled] {
  background: #da291c !important;
  color: #fff !important;
  opacity: 0.5;
}

/* Ant Design Checkbox 自定义选中图标 */
label.ant-checkbox-wrapper .ant-checkbox.ant-checkbox-checked .ant-checkbox-inner::after {
  /* 自定义选中图标样式 */
  border: 0;
  background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAUCAYAAACeXl35AAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAACjSURBVHgB7ZTrEUUwEIU3t4JbghK0oAOdKIEO6YAO6ODYeMwYj7BZ8YdvZsdENvlmOAnRhxIAGVdET8CiHCN1cOlCNlNSKHZkLVdMIRDJeOJPCqSyXPNzfWTwTZT4M9oErRZclsInIL5SaNIoleKO6F+V4s5zdiZFiEN9IC2sVCv77b00xnT8SLgqx9qhh3tdPdu9XZMYb550GtqNm+VYKnsHPdh04ewr4enDAAAAAElFTkSuQmCC');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  width: 15px;
  height: 15px;
  transform: none;
  opacity: 1;
  top: 10%;
  inset-inline-start: 12%;
}
.ant-btn-primary[disabled]:not([disabled='false']) {
  background: #da291c !important;
  color: #fff !important;
  opacity: 0.3;
}

/* SearchModal */
.search-modal.ant-modal .ant-modal-content {
  padding: 20px 48px 36px;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 10%);
}

.search-modal.ant-modal .ant-modal-body {
  padding: 0;
}

/* SearchInput */
.search-input.ant-select-auto-complete.ant-select {
  width: 600px;
  height: 48px;
}

.search-input.ant-select-auto-complete.ant-select .ant-select-selector {
  padding-left: 16px !important;
  padding-right: 16px !important;
  border: none;
  border-radius: 24px;
  color: #86868b;
  background: #f3f3f4;
}

.search-input.ant-select-auto-complete.ant-select
  .ant-select-selector
  input.ant-select-selection-search-input {
  height: 100%;
  color: #0f0f0f;
}

.search-input.ant-select-auto-complete.ant-select .ant-select-clear {
  width: 32px;
  height: 32px;
  margin-top: -16px;
  opacity: 1;
}

/* QuantitySelector */
.number-up-down-field {
  display: flex;
  align-items: center;
  border: 1px solid #f3f3f4;
  border-radius: 100px;
  background-color: #fff;
  width: fit-content;
  overflow: hidden;
}

.number-up-down-field .ant-btn.change-btn {
  border-radius: 0;
  height: 36px;
  width: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  background-color: #f3f3f4;
}

.number-up-down-field .ant-btn.change-btn:disabled {
  background-color: #fff;
  cursor: not-allowed;
  color: #e1e1e4;
}

.number-up-down-field .quantity-input {
  width: 39px;
  border: none;
  border-left: 1px solid #f3f3f4;
  border-right: 1px solid #f3f3f4;
  border-radius: 0;
}

.number-up-down-field .quantity-input .ant-input-number-input {
  height: 36px;
  width: 36px;
  padding: 0;
  text-align: center;
  font-size: 16px;
  line-height: 1.2;
}

.number-up-down-field .quantity-input .ant-input-number-handler-wrap {
  display: none;
}

.number-up-down-field .quantity-input-disabled {
  background: #f5f5f5;
}

.number-up-down-field .quantity-input-disabled .ant-input-number-input {
  color: rgb(0 0 0 / 25%);
}

.number-up-down-field.small .ant-btn.change-btn {
  height: 30px;
  width: 30px;
}

.number-up-down-field.small .quantity-input {
  width: 44px;
}

.number-up-down-field.small .quantity-input .ant-input-number-input {
  height: 30px;
  font-size: 14px;
}

/* Modal */
.custom_modal.ant-modal .ant-modal-content {
  padding: 24px;
  border-radius: 12px;
}

.custom_modal.ant-modal .ant-modal-header {
  margin-bottom: 32px;
}

.custom_modal.ant-modal .ant-modal-title {
  font-size: 20px;
  font-family: var(--font-family-miSansDemiBold450);
  font-weight: 450;
  line-height: 1.4;
}

.custom_modal.ant-modal .ant-modal-footer {
  margin-top: 32px;
}

.custom_modal.ant-modal .ant-modal-close {
  top: 20px;
  right: 20px;
}

.custom_modal.ant-modal .ant-modal-close:hover {
  background-color: inherit;
}

.custom_modal.ant-modal {
  transform-origin: center !important;
}

.ant-modal-root .ant-modal-mask,
.ant-drawer-mask {
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
}
/* RegionSelector Tabs */
.region-selector-tabs .ant-tabs-nav {
  margin-bottom: 0;
}

.region-selector-tabs .ant-tabs-nav::before {
  border-bottom: none;
}

.region-selector-tabs .ant-tabs-tab {
  padding: 8px 24px;
  margin: 0;
}

.region-selector-tabs .ant-tabs-tab-active {
  background: #fff;
}

.region-selector-tabs .ant-tabs-content-holder {
  border: none;
}

/* Checkbox */
label.ant-checkbox-wrapper {
  align-items: center;
  line-height: 1;
}

label.ant-checkbox-wrapper .ant-checkbox .ant-checkbox-inner {
  width: 20px;
  height: 20px;
  border-radius: 50%;
}

label.ant-checkbox-wrapper .ant-checkbox-checked .ant-checkbox-inner::after {
  inset-inline-start: 30%;
}

/* RadioGroup */
.custom-radio-group.ant-radio-group {
  gap: 12px;
  flex-wrap: wrap;
}

.nav-tabs.custom-radio-group.ant-radio-group {
  display: flex;
}

.custom-radio-group.ant-radio-group .ant-radio-button-wrapper {
  padding: 24px 16px;
  height: auto;
  border-width: 1px;
  border-style: solid;
  border-radius: 8px;
}

.nav-tabs.custom-radio-group.ant-radio-group .ant-radio-button-wrapper {
  padding: 0 16px;
  border-color: #f3f3f4;
  border-radius: 100px;
  background: #f3f3f4;
  font-size: 12px;
  line-height: 1;
  height: 35px;
  font-family: var(--font-family-miSansMedium380);
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-tabs.custom-radio-group.ant-radio-group .ant-radio-button-wrapper:hover {
  color: #000000;
}

.nav-tabs.custom-radio-group.ant-radio-group .ant-radio-button-wrapper-checked {
  border-color: #da291c;
  background: #da291c0d;
}

.nav-tabs.custom-radio-group.ant-radio-group .ant-radio-button-wrapper-checked:hover {
  color: #da291c;
}

.order-tabs.nav-tabs.custom-radio-group.ant-radio-group .ant-radio-button-wrapper {
  height: 30px;
}

.custom-radio-group.ant-radio-group .ant-radio-button-wrapper::before {
  display: none;
}

.label-style.custom-radio-group.ant-radio-group .ant-radio-button-wrapper {
  padding: 8px 25px;
  background: #f3f3f4;
  margin-right: 8px;
  border: none;
}

.ant-form-item-control-input-content {
  display: flex;
  gap: 8px;
  align-items: center;
}

/* Cascader */
.ant-select.ant-cascader {
  height: 48px;
}

/* Breadcrumb  */
.ant-breadcrumb a:hover {
  background-color: transparent !important;
}

/* CouponBtn */
.coupon-btn.ant-btn {
  background: #fff;
  color: #da291c;
  border-color: #da291c;
  box-shadow: none;
  border-radius: 100px;
  padding: 4px 12px;
}

.coupon-tabs.ant-tabs .ant-tabs-tab {
  font-size: 20px;
  font-family: var(--font-family-miSansMedium380);
}

.coupon-tabs.ant-tabs .ant-tabs-tab.ant-tabs-tab-active {
  font-family: var(--font-family-miSansDemiBold450);
}

.coupon-tabs.ant-tabs .ant-tabs-nav {
  margin-bottom: 32px;
}
.coupon-tabs.ant-tabs .ant-tabs-nav::before {
  border-bottom: none;
}

.coupon-tabs.ant-tabs .ant-tabs-nav-wrap {
  height: 52px;
}

.coupon-tabs.ant-tabs .ant-tabs-tab + .ant-tabs-tab {
  margin-left: 40px;
}

.coupon-tabs.ant-tabs .ant-tabs-tab-btn {
  color: #6e6e73;
}

/* ====== Checkout Page ====== */
.custom_modal .ant-modal-body .address-form {
  margin-top: 8px;
  font-family: var(--font-family-miSansRegular330);
}

.address-form .ant-form-item .ant-form-item-additional .ant-form-item-explain-error {
  color: #da291c;
  font-family: var(--font-family-miSansRegular330);
  font-size: 12px;
  margin: 4px 0 16px;
}

.address-form .ant-form-item .ant-form-item-row {
  flex-direction: row-reverse;
  gap: 12px;
}

.address-form .ant-form-item .ant-form-item-row .ant-form-item-label {
  padding: 0 !important;
}

.address-form .ant-form-item .ant-form-item-label > label.ant-form-item-no-colon {
  height: 48px;
}

.address-form .ant-form-item .ant-form-item-label > label.ant-form-item-no-colon::after {
  display: none;
}

.address-form .ant-form-item .ant-form-item-label > label.ant-form-item-no-colon::before {
  margin-inline-end: 0 !important;
  color: #da291c;
}

.address-btn.ant-btn span:hover {
  color: #da291c !important;
}

.user-navigation .ant-menu-item {
  gap: 12px;
  margin-left: 0 !important;
  margin-right: 0 !important;
  width: 100% !important;
  padding-left: 24px !important;
  padding-right: 24px !important;
}

/* ReturnApplicationDrawer */
.return-image-uploader.uploader-full.ant-upload-wrapper .ant-upload-select {
  width: 100% !important;
  height: 119px !important;
}
.return-image-uploader.ant-upload-wrapper .ant-upload-list-item-container {
  width: 106px !important;
  height: 106px !important;
  overflow: hidden;
  border-radius: 12px;
}
.return-image-uploader.ant-upload-wrapper .ant-upload-select {
  width: 106px !important;
  height: 106px !important;
  overflow: hidden;
  border-radius: 12px;
  border: none !important;
}

/* OrderDetailProductList */
.order-detail-table.ant-table-wrapper .ant-table-thead > tr > th {
  border-bottom: 1px solid #f3f3f4;
  font-family: var(--font-family-miSansRegular330);
  font-weight: 330;
  font-size: 14px;
  line-height: 1.4;
  color: #222223;
  opacity: 0.6;
  padding: 16px 0;
}

.order-detail-table.ant-table-wrapper .ant-table-tbody .ant-table-cell {
  border-color: #f3f3f4;
  border-left: none;
  border-right: none;
  vertical-align: middle;
}

.order-detail-table.ant-table-wrapper
  .ant-table-tbody
  [data-row-key='address-row']
  .ant-table-cell {
  border: none;
  padding: 24px 0 0;
}

.order-detail-table.ant-table-wrapper .ant-table-tbody tr:last-child .ant-table-cell {
  border: none;
}

/* ChooseTrackCompany */
.ant-dropdown.choose-track-company-dropdown .ant-dropdown-menu-vertical {
  max-height: 304px;
}

.ant-dropdown.choose-track-company-dropdown
  .ant-dropdown-menu-vertical
  .ant-dropdown-menu-item:hover
  .ant-dropdown-menu-title-content {
  color: #da291c;
}

/* ============ 自定义样式覆盖 ============ */

/* 响应式容器 - 根据屏幕宽度调整边距 */
.max-container {
  width: 100%;
  /* min-width: 1024px; */
  max-width: 1440px;
  margin: 0 auto;
  padding: 0 32px;
  margin-bottom: 80px;
  box-sizing: border-box;
}

.max-container-no-mb {
  width: 100%;
  /* min-width: 1024px; */
  max-width: 1440px;
  margin-right: auto;
  margin-left: auto;
  padding: 0 32px;
  box-sizing: border-box;
}

@media screen and (min-width: 1200px) {
  .max-container,
  .max-container-no-mb {
    padding-left: 120px;
    padding-right: 120px;
  }
}

@media screen and (min-width: 1440px) {
  .max-container {
    margin-bottom: 104px;
  }
}

@media (min-width: 1680px) {
  .max-container,
  .max-container-no-mb {
    padding-left: 0;
    padding-right: 0;
  }
}

@media screen and (min-width: 1920px) {
  .max-container {
    margin-bottom: 120px;
  }
}

/* 响应式ModuleBanner高度 */
.responsive-module-banner {
  height: 386px;
}

@media screen and (min-width: 1200px) {
  .responsive-module-banner {
    height: 386px;
  }
}

@media screen and (min-width: 1440px) {
  .responsive-module-banner {
    height: 482px;
  }
}

@media (min-width: 1680px) {
  .responsive-module-banner {
    height: 576px;
  }
}

/* 响应式分类导航宽度 */
.responsive-category-nav {
  width: 316px;
}

@media (min-width: 1024px) and (max-width: 1439px) {
  .responsive-category-nav {
    width: clamp(226px, 226px + ((100vw - 1024px) / 416) * 90, 316px);
  }
}

@media (max-width: 1023px) {
  .responsive-category-nav {
    width: 226px;
  }
}

/* 响应式购物车产品图片容器 */
.responsive-cart-img {
  width: 220px;
  height: 220px;
}

/* 平板设备导航适配 - 禁用hover效果 */
@media (min-width: 768px) and (max-width: 1439px) {
  /* 平板设备上禁用导航项的hover效果，改为点击交互 */
  .group:hover .group-hover\:text-primary {
    color: inherit;
  }

  /* 保持已选中状态的样式 */
  .text-primary {
    color: #da291c !important;
  }
}
@media (min-width: 1024px) and (max-width: 1439px) {
  .responsive-cart-img {
    width: calc(88px + (220 - 88) * ((100vw - 1024px) / (1440 - 1024)));
    height: calc(88px + (220 - 88) * ((100vw - 1024px) / (1440 - 1024)));
  }
}
@media (max-width: 1023px) {
  .responsive-cart-img {
    width: 88px;
    height: 88px;
  }
}

/* 响应式结算产品图片容器 */
.responsive-checkout-img {
  width: 220px;
  height: 220px;
}
@media (min-width: 1024px) and (max-width: 1439px) {
  .responsive-checkout-img {
    width: calc(168px + (220 - 168) * ((100vw - 1024px) / (1440 - 1024)));
    height: calc(168px + (220 - 168) * ((100vw - 1024px) / (1440 - 1024)));
  }
}

@media (max-width: 1023px) {
  .responsive-checkout-img {
    width: 168px;
    height: 168px;
  }
}

.reason-checkbox span:last-child {
  font-size: 16px;
  line-height: 1.4;
  color: #444446;
  font-family: var(--font-family-miSansRegular330);
}

.reason-checkbox.ant-checkbox-wrapper-checked span:last-child {
  color: #0f0f0f;
}

.ant-upload-picture-card-wrapper.return-picture-card:not(.uploader-full) {
  padding: 0 19px 12px;
}

.hover-line {
  background: linear-gradient(90deg, #da291c, #da291c) 0 100% /0 1px no-repeat;
  background-size: 0 1px;
  background-position-x: right;
  transition: background-size 0.3s;

  &:hover {
    background-size: 100% 1px;
    background-position-x: left;
    color: #da291c;
  }
}

.ant-input {
  font-family: var(--font-family-miSansRegular330) !important;
}

.ant-modal-footer {
  .ant-btn {
    padding-top: 8px;
    padding-bottom: 8px;
    font-size: 14px;
    line-height: 20px;
    height: 36px;
  }
}

.invoice-radio .ant-radio-button-wrapper {
  padding-top: 0 !important;
  padding-bottom: 0 !important;
  height: 70px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.invoice-radio .ant-radio-button-wrapper:hover {
  color: #000;
}

.invoice-radio .ant-radio-button-wrapper-checked {
  border-width: 2px !important;
  padding-top: 22px !important;
  padding-bottom: 22px !important;
  color: #000 !important;
}

.payment-radio-group .ant-radio-button-wrapper {
  display: block;
  height: 72px !important;
  padding: 23px 16px !important;
}

.custom-radio-group.payment-radio-group .ant-radio-button-wrapper-checked {
  border-width: 2px !important;
  padding: 22px 15px !important;
  color: #000 !important;
}

.pdp-img-description .render-html-container {
  & div,
  & p,
  & img {
    width: 100% !important;
  }
}

.tooltip-coupon {
  max-width: 478px !important;
}

.tooltip-coupon .ant-tooltip-inner {
  padding: 16px !important;
}

.coupon-desc-content {
  padding: 8px 0;
}
.coupon-desc-content p {
  font-size: 12px;
  font-family: var(--font-family-miSansRegular330);
  line-height: 20px;
  color: #6e6e73;
}
